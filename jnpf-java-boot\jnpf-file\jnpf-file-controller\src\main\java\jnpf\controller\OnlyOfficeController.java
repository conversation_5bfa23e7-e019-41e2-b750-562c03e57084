package jnpf.controller;

import cn.xuyanwu.spring.file.storage.FileInfo;
import io.swagger.v3.oas.annotations.tags.Tag;
import jnpf.config.ConfigValueUtil;
import jnpf.constant.FileTypeConstant;
import jnpf.util.FilePathUtil;
import jnpf.util.FileUploadUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.Collections;
import java.util.Map;

/**
 * OnlyOffice控制器
 */
@Slf4j
@Tag(name = "公共", description = "file")
@RestController
@RequestMapping("/api/onlyoffice")
public class OnlyOfficeController {

    @Autowired
    private ConfigValueUtil configValueUtil;
    private final RestTemplate restTemplate = new RestTemplate();

    @PostMapping("/callback")
    public Map<String, Integer> callback(@RequestBody Map<String, Object> body) throws IOException {
        Integer status = (Integer) body.get("status");
        String key = (String) body.get("key");    // 我们传给前端的唯一标识
        String url = (String) body.get("url");    // DS 提供的下载地址

        if (status != null && (status == 2 || status == 3)) {
            try {
                // 下载文件内容
                byte[] fileBytes = restTemplate.getForObject(url, byte[].class);
                if (fileBytes != null) {
                    // 使用FilePathUtil.getFilePath("annex")获取文件路径
                    String filePath = FilePathUtil.getFilePath(FileTypeConstant.ANNEX);

                    // 处理文件名中可能包含的路径分隔符
                    String fileName = key;

                    // 使用FileUploadUtils上传文件
                    FileInfo fileInfo = FileUploadUtils.uploadFile(fileBytes, filePath, fileName);
                    log.info("OnlyOffice文件保存成功: {}", fileInfo.getFilename());
                } else {
                    log.error("从OnlyOffice下载的文件内容为空, key: {}", key);
                }
            } catch (Exception e) {
                log.error("OnlyOffice文件保存失败, key: {}, error: {}", key, e.getMessage(), e);
                // 即使保存失败，也要返回success，否则OnlyOffice会认为操作失败
            }
        }

        // 必须返回 error:0，否则 DS 认为保存失败
        return Collections.singletonMap("error", 0);
    }

}
