<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6387d353-271f-4e2a-b72e-95c806718adf" name="更改" comment="JNPF增强-支持输出SQL查询日志，方便调试">
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jnpf-common/jnpf-boot-common/jnpf-common-core/src/main/java/jnpf/properties/GatewayWhite.java" beforeDir="false" afterPath="$PROJECT_DIR$/jnpf-common/jnpf-boot-common/jnpf-common-core/src/main/java/jnpf/properties/GatewayWhite.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jnpf-java-boot/jnpf-admin/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jnpf-java-boot/jnpf-admin/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jnpf-java-boot/jnpf-admin/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jnpf-java-boot/jnpf-admin/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jnpf-java-boot/jnpf-file/jnpf-file-controller/src/main/java/jnpf/controller/UtilsController.java" beforeDir="false" afterPath="$PROJECT_DIR$/jnpf-java-boot/jnpf-file/jnpf-file-controller/src/main/java/jnpf/controller/UtilsController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jnpf-scheduletask/xxl-job-admin/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jnpf-scheduletask/xxl-job-admin/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jnpf-workflow/jnpf-workflow-admin/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jnpf-workflow/jnpf-workflow-admin/src/main/resources/application-dev.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="repository" />
    </option>
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="MIXED" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="explicitlyDisabledProfiles" value="encrypted" />
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="ProjectErrors" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2tnYlGlwxjvKVtko7PqjU3i34nJ" />
  <component name="ProjectViewState">
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.jnpf-java-boot [clean].executor": "Run",
    "Maven.jnpf-java-boot [package].executor": "Run",
    "Maven.jnpf-workflow [clean].executor": "Run",
    "Maven.jnpf-workflow [package].executor": "Run",
    "Maven.jnpf-workflow [verify].executor": "Run",
    "Maven.jnpf-workflow-admin [clean].executor": "Run",
    "Maven.jnpf-workflow-admin [package].executor": "Run",
    "Maven.jnpf-workflow-common [clean].executor": "Run",
    "Maven.jnpf-workflow-core [clean].executor": "Run",
    "Maven.jnpf-workflow-core [install].executor": "Run",
    "Maven.jnpf-workflow-core [package].executor": "Run",
    "Maven.kkFileView-parent [clean].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found": "Database connection parameters found",
    "Notification.DoNotAsk-DatabaseConfigFileWatcher.found": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.JnpfAdminApplication.executor": "Debug",
    "Spring Boot.JnpfFlowableApplication.executor": "Run",
    "Spring Boot.ReportUniverApplication.executor": "Run",
    "Spring Boot.XxlJobAdminApplication.executor": "Run",
    "Spring Boot.kkFileViewServerMain.executor": "Run",
    "WebServerToolWindowFactoryState": "false",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "/Users/<USER>/work/jnpf/jnpf5.2/jnpf5.2/jnpf-web-tenant-vue3/src/assets/images",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "pnpm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.373062",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "preferences.sourceCode.XML",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "ChangesTree.GroupingKeys": [
      "directory",
      "repository"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/jnpf-web-tenant-vue3/src/assets/images" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\ProjectsTest\JavaWeb\JNPF-5.2.0-Java\jnpf-file-preview\server\src\main\resources\static\static\css" />
      <recent name="D:\ProjectsTest\JavaWeb\JNPF-5.2.0-Java\jnpf-file-preview\server\src\main\resources\static\css" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.JnpfAdminApplication">
    <configuration default="true" type="AZURE_FUNCTION_SUPPORT" factoryName="Run Functions">
      <option name="appName" />
      <option name="appServicePlanName" />
      <option name="appServicePlanResourceGroup" />
      <option name="appSettingsKey" value="03ccda92-60cc-413f-a308-c93a0af50aad" />
      <option name="artifact" />
      <option name="debugOptions" />
      <option name="deployment" />
      <option name="deploymentStagingDirectoryPath" />
      <option name="funcPath" />
      <option name="functionHostArguments" />
      <option name="hostJsonPath" />
      <option name="insightsName" />
      <option name="instrumentationKey" />
      <option name="javaVersion" />
      <option name="localSettingsJsonPath" />
      <option name="moduleName" />
      <option name="os" />
      <option name="pricingTier" />
      <option name="providerMap">
        <map />
      </option>
      <option name="region" />
      <option name="resourceGroup" />
      <option name="stagingFolder" />
      <option name="subscription" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JnpfAdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="jnpf-admin" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="jnpf.JnpfAdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JnpfFlowableApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="jnpf-workflow-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="jnpf.JnpfFlowableApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JnpfTenantApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="jnpf-tenant" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="jnpf.JnpfTenantApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ReportUniverApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="jnpf-datareport-univer-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="jnpf.ReportUniverApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="XxlJobAdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="xxl-job-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xxl.job.admin.XxlJobAdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="kkFileViewServerMain" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="kkFileView" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.keking.ServerMain" />
      <option name="WORKING_DIRECTORY" value="file://jnpf-file-preview" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.JnpfAdminApplication" />
      <item itemvalue="Spring Boot.JnpfFlowableApplication" />
      <item itemvalue="Spring Boot.kkFileViewServerMain" />
      <item itemvalue="Spring Boot.XxlJobAdminApplication" />
      <item itemvalue="Spring Boot.JnpfTenantApplication" />
      <item itemvalue="Spring Boot.ReportUniverApplication" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="6387d353-271f-4e2a-b72e-95c806718adf" name="更改" comment="" />
      <created>1740989455346</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1740989455346</updated>
      <workItem from="1740989457103" duration="5322000" />
      <workItem from="1741067122510" duration="121000" />
      <workItem from="1741067289438" duration="10957000" />
      <workItem from="1741671391421" duration="1243000" />
      <workItem from="1741771298405" duration="21000" />
      <workItem from="1741771338673" duration="605000" />
      <workItem from="1741854911679" duration="965000" />
      <workItem from="1753326027869" duration="84000" />
      <workItem from="1753326146094" duration="16565000" />
      <workItem from="1753405572266" duration="29076000" />
      <workItem from="1753665382868" duration="2878000" />
      <workItem from="1753670398088" duration="18390000" />
      <workItem from="1753859676438" duration="16370000" />
      <workItem from="1754270651314" duration="32977000" />
      <workItem from="1754530432302" duration="8055000" />
      <workItem from="1754617760508" duration="16644000" />
      <workItem from="1754879208198" duration="34223000" />
      <workItem from="1755136346053" duration="41972000" />
      <workItem from="1755481695766" duration="24083000" />
    </task>
    <task id="LOCAL-00001" summary="init jnpf-file-preview">
      <option name="closed" value="true" />
      <created>1753443683469</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753443683469</updated>
    </task>
    <task id="LOCAL-00002" summary="LFS init file-preview">
      <option name="closed" value="true" />
      <created>1754449935551</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754449935551</updated>
    </task>
    <task id="LOCAL-00003" summary="补充文件">
      <option name="closed" value="true" />
      <created>1754620183381</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754620183381</updated>
    </task>
    <task id="LOCAL-00004" summary="补充文件">
      <option name="closed" value="true" />
      <created>1754620293038</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754620293038</updated>
    </task>
    <task id="LOCAL-00005" summary="补充文件">
      <option name="closed" value="true" />
      <created>1754620323590</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1754620323590</updated>
    </task>
    <task id="LOCAL-00006" summary="补充文件">
      <option name="closed" value="true" />
      <created>1754620376881</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1754620376881</updated>
    </task>
    <task id="LOCAL-00007" summary="补充.idea配置">
      <option name="closed" value="true" />
      <created>1754623183514</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1754623183514</updated>
    </task>
    <task id="LOCAL-00008" summary="补充jar包">
      <option name="closed" value="true" />
      <created>1754633497758</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1754633497758</updated>
    </task>
    <task id="LOCAL-00009" summary="JNPF权限增强-支持关联用户主管的所有部门成员">
      <option name="closed" value="true" />
      <created>1755272626057</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1755272626057</updated>
    </task>
    <task id="LOCAL-00010" summary="JNPF权限增强-支持关联用户主管的所有部门成员">
      <option name="closed" value="true" />
      <created>1755482093561</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1755482093561</updated>
    </task>
    <task id="LOCAL-00011" summary="JNPF增强-支持输出SQL查询日志，方便调试">
      <option name="closed" value="true" />
      <created>1755482257836</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1755482257836</updated>
    </task>
    <option name="localTasksCounter" value="12" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="java:org.apache.dubbo:dubbo" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.integration:spring-integration-core" />
    <option featureType="dependencySupport" implementationName="java:org.seleniumhq.selenium:selenium-java" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="executable:kubectl" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-context" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:javax.validation:validation-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
    <option featureType="dependencySupport" implementationName="java:io.reactivex.rxjava2:rxjava" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-stream" />
    <option featureType="dependencySupport" implementationName="java:junit:junit" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="executable:docker" />
    <option featureType="dependencySupport" implementationName="java:io.grpc:grpc-api" />
    <option featureType="dependencySupport" implementationName="java:io.reactivex.rxjava3:rxjava" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="init jnpf-file-preview" />
    <MESSAGE value="LFS init file-preview" />
    <MESSAGE value="补充配置" />
    <MESSAGE value="补充配置" />
    <MESSAGE value="补充.idea配置" />
    <MESSAGE value="补充jar包" />
    <MESSAGE value="JNPF权限增强-支持关联用户主管的所有部门成员" />
    <MESSAGE value="JNPF增强-支持输出SQL查询日志，方便调试" />
    <option name="LAST_COMMIT_MESSAGE" value="JNPF增强-支持输出SQL查询日志，方便调试" />
    <option name="NON_MODAL_COMMIT_POSTPONE_SLOW_CHECKS" value="false" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jnpf-file-preview/server/src/main/java/cn/keking/service/OfficePluginManager.java</url>
          <line>79</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jnpf-file-preview/server/src/main/java/cn/keking/web/controller/OnlinePreviewController.java</url>
          <line>92</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jnpf-java-boot/jnpf-flowable/jnpf-flowable-biz/src/main/java/jnpf/flowable/util/TriggerUtil.java</url>
          <line>632</line>
          <option name="timeStamp" value="18" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jnpf-java-boot/jnpf-visualdev/jnpf-visualdev-base/jnpf-visualdev-base-biz/src/main/java/jnpf/base/util/VisualUtil.java</url>
          <line>372</line>
          <option name="timeStamp" value="19" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jnpf-java-boot/jnpf-visualdev/jnpf-visualdev-base/jnpf-visualdev-base-biz/src/main/java/jnpf/base/util/VisualUtil.java</url>
          <line>333</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jnpf-java-boot/jnpf-visualdev/jnpf-visualdev-base/jnpf-visualdev-base-biz/src/main/java/jnpf/base/util/VisualUtil.java</url>
          <line>254</line>
          <option name="timeStamp" value="21" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jnpf-java-boot/jnpf-visualdev/jnpf-visualdev-onlinedev/jnpf-visualdev-onlinedev-biz/src/main/java/jnpf/onlinedev/util/onlineDevUtil/OnlineSwapDataUtils.java</url>
          <line>802</line>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jnpf-java-boot/jnpf-file/jnpf-file-controller/src/main/java/jnpf/controller/OnlyOfficeController.java</url>
          <line>52</line>
          <option name="timeStamp" value="35" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jnpf-common/jnpf-boot-common/jnpf-common-file/src/main/java/jnpf/util/FileUploadUtils.java</url>
          <line>118</line>
          <option name="timeStamp" value="36" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>