# 应用服务器
server:
  tomcat:
    uri-encoding: UTF-8 #tomcat编码
  port: 30000 #tomcat端口

spring:
  messages:
    basename: i18n/message
    # 一天刷新一次
    cache-duration: 24H
  devtools: #spring开发者工具模块
    restart:
      enabled: true #热部署开关
    freemarker:
      cache: false #spring内置freemarker缓存
  thymeleaf:
    cache: false #spring内置thymeleaf缓存

  # ===================== 数据源配置 =====================
  exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure #排除自动配置，手动配置druid
  datasource:
    db-type: MySQL #数据库类型(可选值 MySQL、SQLServer、Oracle、DM、KingbaseES、PostgreSQL，请严格按可选值填写)
    host: ***********
    port: 23306
    username: root
    password: pz123456
    db-name: jnpf_init_demo
    db-schema: #金仓达梦选填
    prepare-url: #自定义url

    # ===================== 动态多数据源 =====================
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: true #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      druid:
        # 空闲时执行连接测试
        test-while-idle: true
        # 连接测试最小间隔
        time-between-eviction-runs-millis: 60000
        # 获取连接等待3秒 根据网络情况设定
        max-wait: 3000
        # 初始化4个连接
        initial-size: 4
        # 最大20个连接
        max-active: 20
        # 最少保持4个空闲连接
        min-idle: 4
        # 空闲连接保活, 超过配置的空闲时间会进行连接检查完成保活操作(数据库自身会断开达到空闲时间的连接， 程序使用断开的连接会报错)
        keep-alive: true
        # 连接超时
        connect-timeout: 10000
        # 连接超时
        socket-timeout: 10000
        # 查询超时
        query-timeout: 90000
        # 事务查询超时
        transaction-query-timeout: 90000
        # 解除注释后Druid连接池打印SQL语句 忽略日志等级配置
        # filters: slf4j
        slf4j:
          statementLogEnabled: true
          resultSetLogEnabled: false
          connectionLogEnabled: false
          dataSourceLogEnabled: false
          statementCreateAfterLogEnabled: false
          statementCloseAfterLogEnabled: false
          statementExecuteAfterLogEnabled: false
          #打印SQL替换参数
          statementExecutableSqlLogEnable: true
          statementPrepareAfterLogEnabled: false
          statementPrepareCallAfterLogEnabled: false
          statementParameterSetLogEnabled: false
  #      datasource:
  #        master:
  #          url: jdbc:mysql://${spring.datasource.host}:${spring.datasource.port}/${spring.datasource.dbname}?useUnicodebase=true&characterEncoding=utf-8&useSSL=false&allowMultiQueries=true&serverTimezone=UTC
  #          username: ${spring.datasource.username}
  #          password: ${spring.datasource.password}
  #          driver-class-name: com.mysql.cj.jdbc.Driver

  # ===================== Redis配置-Start =====================
  # redis单机模式
  redis:
    database: 1 #缓存库编号
    host: 127.0.0.1
    port: 6379
    #password: 123456  # 密码为空时，请将本行注释
    timeout: 3000 #超时时间(单位：秒)
    lettuce: #Lettuce为Redis的Java驱动包
      pool:
        max-active: 8 # 连接池最大连接数
        max-wait: -1ms  # 连接池最大阻塞等待时间（使用负值表示没有限制）
        min-idle: 0 # 连接池中的最小空闲连接
        max-idle: 8 # 连接池中的最大空闲连接

  # redis集群模式
  #  redis:
  #    cluster:
  #      nodes:
  #        - *************:6380
  #        - *************:6381
  #        - *************:6382
  #        - *************:6383
  #        - *************:6384
  #        - *************:6385
  #    password: 123456 # 密码为空时，请将本行注释
  #    timeout: 3000 # 超时时间(单位：秒)
  #    lettuce: #Lettuce为Redis的Java驱动包
  #      pool:
  #        max-active: 8 # 连接池最大连接数
  #        max-wait: -1ms  # 连接池最大阻塞等待时间（使用负值表示没有限制）
  #        min-idle: 0 # 连接池中的最小空闲连接
  #        max-idle: 8 # 连接池中的最大空闲连接
  # ===================== Redis配置-End =====================
  # ===================== 单点登录(用户信息同步)配置-Start =====================
  cloud:
    stream:
      # 若使用RocketMQ-Start
      #rocketmq:
      #binder:
      #name-server: ************:30094
      #group: maxkey_identity
      # 若使用RocketMQ-End
      # 若使用RabbitMQ-Start
      binders:
        defaultRabbit: # 表示定义的名称，用于binding整合
          type: rabbit # 消息组件类型
          environment: # 设置rabbitmq的相关环境配置
            spring:
              rabbitmq:
                host: 127.0.0.1
                port: 5672
                username: admin
                password: pwd_MQ@rabbit.2023
              # 若使用RabbitMQ-End
      # 若使用Kafka-Start
      #      kafka:
      #        # KafkaBinderConfigurationProperties
      #        binder:
      #          brokers: ************:9092
      # 若使用Kafka-End
      bindings:
        ssoEventReceiver-in-0:
          content-type: text/json
          destination: MXK_IDENTITY_MAIN_TOPIC
          group: maxkey_identity
    # ===================== 单点登录(用户信息同步)配置-End =====================
    # ===================== AI配置-Start =====================
    ai:
      openai:
        enabled: true
        # 超时时间, 秒, 根据AI平台性能调整超时时间
        timeout: 300

        # 阿里百联平台
        #        api-host: https://dashscope.aliyuncs.com/compatible-mode/
        #        api-key:
        #        chat:
        #          mode: qwen2.5-1.5b-instruct

        # GPT转发平台
        #        api-host: https://api.chatanywhere.tech/
        #        api-key:
        #        chat:
        #          mode: gpt-3.5-turbo

        # DeepSeek
        #        api-host: https://api.deepseek.com/v1/
        #        api-key:
        #        chat:
        #          mode: deepseek-chat

        api-host: https://integrate.api.nvidia.com/v1
        api-key: **********************************************************************
        chat:
          mode: deepseek-ai/deepseek-r1


  # ===================== AI配置-End =====================
# SpringDoc接口文档 访问地址：http://127.0.0.1:30000/doc.html
springdoc:
  default-flat-param-object: true
  api-docs:
    enabled: true
#SpringDoc增强
#knife4j:
#  enable: true
#  basic: #接口文档访问鉴权
#    enable: true
#    username: jnpf
#    password: 123456

lock4j:
  aop:
    # Lock4j注解是否启用
    enabled: false

config:
  # ===================== 是否开启测试环境 =====================
  TestVersion: false
  # ===================== ApacheShardingSphere 配置开关 =====================
  sharding-sphere-enabled: false
  # ===================== 文件存储配置-Start =====================
  file-storage: #文件存储配置，不使用的情况下可以不写
    default-platform: local-plus-1 #默认使用的存储平台
    thumbnail-suffix: ".thumb.jpg" #缩略图后缀，例如【.min.jpg】【.png】
    local-plus: # 本地存储升级版
      - platform: local-plus-1 # 存储平台标识
        enable-storage: true  #启用存储
        enable-access: true #启用访问（线上请使用 Nginx 配置，效率更高）
        domain: "" # 访问域名，例如：“http://127.0.0.1:8030/”，注意后面要和 path-patterns 保持一致，“/”结尾，本地存储建议使用相对路径，方便后期更换域名
        base-path: D:\ProjectsTest\JavaWeb\JNPF-5.2.0-Other\jnpf-resources\ # 基础路径
        path-patterns: /** # 访问路径
        storage-path:  # 存储路径
    aliyun-oss: # 阿里云 OSS ，不使用的情况下可以不写
      - platform: aliyun-oss-1 # 存储平台标识
        enable-storage: false  # 启用存储
        access-key: ??
        secret-key: ??
        end-point: ??
        bucket-name: ??
        domain: ?? # 访问域名，注意“/”结尾，例如：https://abc.oss-cn-shanghai.aliyuncs.com/
        base-path: hy/ # 基础路径
    qiniu-kodo: # 七牛云 kodo ，不使用的情况下可以不写
      - platform: qiniu-kodo-1 # 存储平台标识
        enable-storage: false  # 启用存储
        access-key: ??
        secret-key: ??
        bucket-name: ??
        domain: ?? # 访问域名，注意“/”结尾，例如：http://abc.hn-bkt.clouddn.com/
        base-path: base/ # 基础路径
    tencent-cos: # 腾讯云 COS
      - platform: tencent-cos-1 # 存储平台标识
        enable-storage: false  # 启用存储
        secret-id: ??
        secret-key: ??
        region: ?? #存仓库所在地域
        bucket-name: ??
        domain: ?? # 访问域名，注意“/”结尾，例如：https://abc.cos.ap-nanjing.myqcloud.com/
        base-path: hy/ # 基础路径
    minio: # MinIO，由于 MinIO SDK 支持 AWS S3，其它兼容 AWS S3 协议的存储平台也都可配置在这里
      - platform: minio-1 # 存储平台标识
        enable-storage: false  # 启用存储
        access-key: 9Y3sjaDWgbxKjXjm
        secret-key: Bs2GyJwmOLpqNsQwbDjdinyUJQHtM0rc
        end-point: http://192.168.0.207:9000/
        bucket-name: v350
        domain: ${config.file-storage.minio[0].end-point} # 访问域名，注意“/”结尾，例如：http://minio.abc.com/abc/
        base-path:  # 基础路径
  # ===================== 文件存储配置-End =====================
# ===================== 第三方登录配置-Start =====================
socials:
  # 第三方登录功能开关(false-关闭，true-开启)
  socials-enabled: false
  config:
    - # 微信
      provider: wechat_open
      client-id: your-client-id
      client-secret: your-client-secret
    - # qq
      provider: qq
      client-id: your-client-id
      client-secret: your-client-secret
    - # 企业微信
      provider: wechat_enterprise
      client-id: your-client-id
      client-secret: your-client-secret
      agentId: your-agentId
    - # 钉钉
      provider: dingtalk
      client-id: your-client-id
      client-secret: your-client-secret
      agentId: your-agentId
    - # 飞书
      provider: feishu
      client-id: your-client-id
      client-secret: your-client-secret
    - # 小程序
      provider: wechat_applets
      client-id: your-client-id
      client-secret: your-client-secret
# ===================== 第三方登录配置-End =====================
# ===================== 任务调度配置-Start =====================
xxl:
  job:
    accessToken: '432e62f3b488bc861d91b0e274e850cc'
    i18n: zh_CN
    logretentiondays: 30
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
    # xxl-job服务端地址
    admin:
      addresses: http://127.0.0.1:30020/xxl-job-admin/
    executor:
      address: ''
      appname: xxl-job-executor-sample1
      ip: ''
      logpath: /opt/jnpf/xxl-job/jobhandler
      logretentiondays: 30
      port: 39999
  # rest调用xxl-job接口地址
  admin:
    register:
      handle-query-address: ${xxl.job.admin.addresses}api/handler/queryList
      job-info-address: ${xxl.job.admin.addresses}api/jobinfo
      log-query-address: ${xxl.job.admin.addresses}api/log
      task-list-address: ${xxl.job.admin.addresses}api/ScheduleTask/List
      task-info-address: ${xxl.job.admin.addresses}api/ScheduleTask/getInfo
      task-save-address: ${xxl.job.admin.addresses}api/ScheduleTask
      task-update-address: ${xxl.job.admin.addresses}api/ScheduleTask
      task-remove-address: ${xxl.job.admin.addresses}api/ScheduleTask/remove
      task-start-or-remove-address: ${xxl.job.admin.addresses}api/ScheduleTask/updateTask
# ===================== 任务调度配置-End =====================
# ===================== 单点登录(SSO)配置-Start =====================
jnpf:
  sso:
    # ===================== 单点登录(用户信息拉取)配置-Start =====================
    connector:
      # 是否开启用户信息拉取
      enabled: false
    # ===================== 单点登录(用户信息拉取)配置-End =====================
    # ===================== 单点登录(用户信息推送)配置-Start =====================
    pull:
      # 是否开启用户信息推送
      enabled: false
      create-rest-address: http://localhost:9526/sso-mgt-api/api/idm/Account
      replace-rest-address: http://localhost:9526/sso-mgt-api/api/idm/Account
      change-password-rest-address: http://localhost:9526/sso-mgt-api/api/idm/Account/changePassword
      delete-rest-address: http://localhost:9526/sso-mgt-api/api/idm/Account
      credential-type: Basic
      user-name: 747887288041603072
      password: MYgMMjIwNzIwMjIxNTU4MTAxNzQlKQ
    # ===================== 单点登录(用户信息推送)配置-End =====================
oauth:
  #启用单点登录, 普通登录不可用
  ssoEnabled: false
  #轮询票据有效期
  ticketTimeout: 120
  #默认单点登录协议
  defaultSSO: cas
  #后端登录接口地址
  loginPath: http://127.0.0.1:30000/api/oauth/Login
    #login:
    #JWT生成秘钥 不填写为默认值
  #jwtSecretKey: WviMjFNC72VKwGqm5LPoheQo5XN9iN4d
  sso:
    #单点登录系统地址
    baseUrl: http://127.0.0.1:8527
    #登录成功后跳转到前端的页面
    sucessFrontUrl: http://127.0.0.1:3100/sso
    #错误信息是否输出到页面
    ticketOutMessage: false
    #logoutFrontUrl: http://sso.maxkey.top:8527/maxkey
    #单点注销后端接口地址, 配置启用后JNPF退出会请求单点系统退出, 触发单点注销退出全部应用
    #ssoLogoutApiUrl: ${oauth.sso.baseUrl}/sign/logout
    auth2:
      enabled: true
      clientId: 747887288041603072
      clientSecret: MYgMMjIwNzIwMjIxNTU4MTAxNzQlKQ
      baseUrl: ${oauth.sso.baseUrl}
      authorizeUrl: ${oauth.sso.auth2.baseUrl}/sign/authz/oauth/v20/authorize
      accessTokenUrl: ${oauth.sso.auth2.baseUrl}/sign/authz/oauth/v20/token
      userInfoUrl: ${oauth.sso.auth2.baseUrl}/sign/api/oauth/v20/me
    cas:
      enabled: true
      baseUrl: ${oauth.sso.baseUrl}
      serverLoginUrl: ${oauth.sso.cas.baseUrl}/sign/authz/cas/login
      serverValidateUrl: ${oauth.sso.cas.baseUrl}/sign/authz/cas
# ===================== 单点登录(SSO)配置-End =====================
minio:
  end-point: http://192.168.1.3:9090
  access-key: WM9i3Jus9V8sHoaqQWED
  secret-key: cuBYuHTNxCDet02KpxQI6qQb2RJlFuTYlkh8J0HG
